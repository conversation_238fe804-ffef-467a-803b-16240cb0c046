/* AureaVoice Platform Styles */

body {
    font-family: 'Inter', sans-serif;
    background-color: #f8fafc; /* slate-50 */
}

.chart-container {
    position: relative;
    width: 100%;
    height: 300px;
}

.main-gradient {
     background: linear-gradient(135deg, #0079FF, #004AAD);
}

.category-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}


/* Subtle hover effect for category cards */
.category-card:hover {
    transform: translateY(-2px) scale(1.015);
    box-shadow: 0 4px 12px -2px rgb(0 0 0 / 0.08);
    background-color: #fff;
    border-color: #e2e8f0;
    transition: box-shadow 0.2s, transform 0.2s;
}

/* --- Struktur CSS Grid --- */
.dashboard-grid {
    display: grid;
    gap: 1rem; /* Gap dikurangi setengahnya */
    grid-template-columns: 1fr;
    grid-template-areas:
        "main"
        "sidebar";
}

@media (min-width: 1024px) {
    .dashboard-grid {
        grid-template-columns: repeat(3, 1fr);
        grid-template-areas: "main main sidebar";
    }
}

.main-column {
    grid-area: main;
}

.sidebar-column {
    grid-area: sidebar;
}

.category-grid {
    display: grid;
    gap: 0.75rem; /* Gap dikurangi setengahnya */
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
}

@media (max-width: 768px) {
    .category-grid {
        grid-template-columns: 1fr;
        grid-template-rows: auto;
    }
}
