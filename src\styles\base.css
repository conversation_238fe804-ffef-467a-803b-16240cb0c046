
*, *::before, *::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: #e2e8f0;
  color: #2d3748;
  line-height: 1.6;
  min-height: 100vh;
}

/* Default styles for main app */
body:not(.dashboard-mode) {
  overflow: hidden;
}

body:not(.dashboard-mode) #app {
  width: 100%;
  height: 100vh;
  opacity: 0;
}

/* Dashboard mode styles */
body.dashboard-mode {
  overflow: auto;
  background-color: #f8fafc;
}

body.dashboard-mode #app {
  width: 100%;
  min-height: 100vh;
  opacity: 1;
}


