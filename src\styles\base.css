
*, *::before, *::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: #e2e8f0;
  color: #2d3748;
  line-height: 1.6;
  min-height: 100vh;
}

/* Default styles for main app */
body:not(.dashboard-mode) {
  overflow: hidden;
}

body:not(.dashboard-mode) #app {
  width: 100%;
  height: 100vh;
  opacity: 0;
}

/* Dashboard mode styles */
body.dashboard-mode {
  overflow: auto;
  background-color: #f8fafc;
}

body.dashboard-mode #app {
  width: 100%;
  min-height: 100vh;
  opacity: 1;
}

/* Custom Scrollbar - AureaVoice Theme */
/* Webkit browsers (Chrome, Safari, Edge) */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9; /* slate-100 */
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #0079FF, #004AAD);
  border-radius: 4px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #004AAD, #0079FF);
}

::-webkit-scrollbar-corner {
  background: #f1f5f9;
}

/* Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: #0079FF #f1f5f9;
}


