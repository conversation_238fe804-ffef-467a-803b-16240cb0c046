/* AureaVoice Dashboard Styles */

/* Dashboard Container */
.dashboard-container {
  min-height: 100vh;
  background-color: #f8fafc; /* slate-50 */
  color: #1f2937; /* gray-800 */
  font-family: 'Inter', sans-serif;
}

/* Navigation */
.dashboard-nav {
  background-color: #ffffff;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  border-bottom: 1px solid #e2e8f0; /* slate-200 */
  position: sticky;
  top: 0;
  z-index: 10;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo {
  font-size: 2rem;
  font-weight: 700;
  color: #2563eb; /* blue-600 */
}

.nav-button {
  background-color: #374151; /* slate-700 */
  color: #ffffff;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  text-decoration: none;
  font-size: 1rem;
  font-weight: 600;
  transition: background-color 0.2s;
}

.nav-button:hover {
  background-color: #1f2937; /* slate-800 */
}

/* Main Content */
.dashboard-main {
  max-width: 2000px;
  margin: 0 auto;
  padding: 1rem 2rem;
}

@media (min-width: 768px) {
  .dashboard-main {
    padding: 2rem;
  }
}

/* Header */
.dashboard-header {
  margin-bottom: 2rem;
}

.dashboard-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #111827; /* gray-900 */
  margin-bottom: 0.5rem;
}

.dashboard-subtitle {
  color: #6b7280; /* gray-500 */
  font-size: 1.125rem;
}

/* Cards */
.dashboard-card {
  background-color: #ffffff;
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  padding: 2rem;
  border: 1px solid #f1f5f9;
  overflow: hidden;
}

/* Chart Card Specific */
.chart-card {
  padding: 2rem 1.5rem;
  min-height: 600px;
}

.card-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
}

/* Recommendation Card */
.recommendation-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: #dbeafe; /* blue-50 */
  border-radius: 0.5rem;
  border: 1px solid #bfdbfe; /* blue-100 */
}

.recommendation-icon {
  font-size: 2.5rem;
  margin-right: 1.5rem;
}

.recommendation-content {
  flex-grow: 1;
}

.recommendation-title {
  font-weight: 600;
  color: #1e40af; /* blue-800 */
  font-size: 1.125rem;
}

.recommendation-description {
  font-size: 1rem;
  color: #64748b; /* slate-600 */
  margin-top: 0.5rem;
}

.recommendation-button {
  margin-left: 1.5rem;
  background-color: #2563eb; /* blue-600 */
  color: #ffffff;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  border: none;
  font-size: 1rem;
  flex-shrink: 0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.recommendation-button:hover {
  background-color: #1d4ed8; /* blue-700 */
}

/* Chart Container */
.chart-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 280px;
  padding: 1rem;
  background-color: #f8fafc;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-container canvas {
  display: block !important;
  box-sizing: border-box !important;
  max-width: 100% !important;
  max-height: 100% !important;
  border-radius: 0.375rem;
  background-color: transparent;
}

/* Profile Card */
.profile-card {
  background-color: #ffffff;
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  border: 1px solid #f1f5f9; /* slate-100 */
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 900px;
  overflow: hidden;
}

.main-gradient {
  background: linear-gradient(135deg, #0079FF, #004AAD);
  color: #ffffff;
  border-radius: 0.75rem 0.75rem 0 0;
  padding: 2.5rem 1.5rem;
  text-align: center;
}

.score-label {
  color: #bfdbfe; /* blue-200 */
  font-size: 1.25rem;
}

.score-value {
  font-size: 4.5rem;
  font-weight: 900;
  margin: 0.75rem 0;
}

.score-percentage {
  font-size: 2.25rem;
  font-weight: 700;
}

.score-improvement {
  color: #bfdbfe; /* blue-200 */
  font-size: 1rem;
}

.profile-content {
  padding: 2.5rem 2rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.profile-title {
  font-weight: 700;
  font-size: 1.375rem;
  text-align: center;
  margin-bottom: 1.5rem;
}

.profile-image-container {
  margin: 0 auto 1.5rem auto;
}

.profile-image {
  width: 7rem;
  height: 7rem;
  border-radius: 50%;
  border: 4px solid #ffffff;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

.stats-container {
  margin-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
  padding-top: 2rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f1f5f9; /* slate-100 */
  padding: 1rem 1.25rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.stat-label {
  font-size: 1rem;
  color: #64748b; /* slate-600 */
}

.stat-value {
  font-weight: 700;
  color: #1e293b; /* slate-800 */
  font-size: 1.125rem;
}

/* Grid Layouts */
.dashboard-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
  grid-template-areas:
    "main"
    "sidebar";
  max-height: 900px;
  height: 900px;
}

@media (min-width: 1024px) {
  .dashboard-grid {
    grid-template-columns: 1fr 550px;
    grid-template-areas: "main sidebar";
    gap: 30px;
  }
}

.main-column {
  grid-area: main;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 20px;
  height: 100%;
  max-height: 900px;
}

/* Recommendation card should be at top */
.main-column > .dashboard-card:first-child {
  flex-shrink: 0;
}

/* Chart card should be at bottom and take remaining space */
.main-column > .dashboard-card:last-child {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.main-column > .dashboard-card:last-child .chart-container {
  flex-grow: 1;
  height: auto;
  min-height: 500px;
}

.sidebar-column {
  grid-area: sidebar;
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 900px;
}



/* Category Grid */
.categories-section {
  margin-top: 3rem;
}

.categories-title {
  font-size: 1.875rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
}

.category-grid {
  display: grid;
  gap: 0.75rem;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
}

@media (max-width: 768px) {
  .category-grid {
    grid-template-columns: 1fr;
    grid-template-rows: auto;
  }
}

.category-card {
  display: block;
  background-color: #ffffff;
  padding: 2rem;
  border-radius: 0.75rem;
  border: 1px solid #e2e8f0; /* slate-200 */
  text-decoration: none;
  color: inherit;
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.category-card:hover {
  transform: translateY(-2px) scale(1.015);
  box-shadow: 0 4px 12px -2px rgb(0 0 0 / 0.08);
  background-color: #fff;
  border-color: #e2e8f0;
}

.category-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.category-title {
  font-weight: 700;
  font-size: 1.375rem;
  margin-bottom: 0.5rem;
}

.category-description {
  font-size: 1rem;
  color: #6b7280; /* gray-500 */
}
